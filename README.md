# 短视频管理平台

基于 React + TypeScript + Vite + Semi UI 构建的短视频管理平台。

## 功能特性

- 🎯 **用户管理** - 创建、编辑、删除用户，支持头像预览
- 📹 **内容管理** - 管理图片和视频内容，支持分类筛选
- 🖼️ **媒体文件管理** - 上传和管理媒体文件，支持预览和排序
- 💬 **评论系统** - 支持主评论和回复评论的完整评论系统
- 📊 **数据统计** - 仪表盘展示各类数据统计
- 🎨 **现代化UI** - 基于 Semi UI 的美观界面
- 📱 **响应式设计** - 适配不同屏幕尺寸

## 技术栈

- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI 组件库**: Semi UI
- **路由管理**: React Router DOM
- **HTTP 客户端**: Axios
- **状态管理**: React Hooks

## 项目结构

```
src/
├── components/          # 可复用组件
│   ├── Comment/        # 评论相关组件
│   ├── Content/        # 内容相关组件
│   ├── Layout/         # 布局组件
│   ├── Media/          # 媒体文件组件
│   ├── User/           # 用户相关组件
│   └── ErrorBoundary.tsx
├── hooks/              # 自定义 Hooks
│   └── useApi.ts       # API 调用 Hook
├── pages/              # 页面组件
│   ├── Dashboard.tsx   # 仪表盘
│   ├── UserManagement.tsx
│   ├── ContentManagement.tsx
│   └── CommentManagement.tsx
├── services/           # API 服务层
│   ├── api.ts          # 基础 API 配置
│   ├── userService.ts
│   ├── contentService.ts
│   ├── mediaService.ts
│   └── commentService.ts
├── types/              # TypeScript 类型定义
│   └── index.ts
├── utils/              # 工具函数
│   └── format.ts
├── App.tsx             # 主应用组件
├── main.tsx            # 应用入口
└── index.css           # 全局样式
```

## 快速开始

### 环境要求

- Node.js >= 16
- npm >= 8

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

应用将在 http://localhost:3000 启动

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## API 接口

本项目对接后端 API，基础 URL 为 `http://localhost:8080`

### 主要接口模块

- **用户管理**: `/api/users`
- **内容管理**: `/api/contents`
- **媒体文件**: `/api/content-media`
- **评论管理**: `/api/comments`

详细的 API 文档请参考后端服务文档。

## 主要功能

### 1. 用户管理
- 用户列表展示，支持分页和搜索
- 创建新用户，支持头像预览
- 编辑用户信息
- 删除用户（带确认提示）

### 2. 内容管理
- 内容列表，支持按用户和类型筛选
- 创建图片/视频内容
- 编辑内容信息
- 媒体文件管理（点击"媒体"按钮）

### 3. 媒体文件管理
- 为内容添加媒体文件
- 支持图片和视频预览
- 媒体文件排序
- 批量管理媒体文件

### 4. 评论系统
- 主评论和回复评论
- 评论列表，支持多维度筛选
- 评论内容管理
- 评论层级关系展示

### 5. 数据统计
- 用户、内容、评论数量统计
- 内容类型分布
- 数据可视化展示

## 开发说明

### 自定义 Hook

项目使用 `useApi` Hook 统一处理 API 调用：

```typescript
const { loading, execute, data, error } = useApi(apiFunction, {
  showSuccessToast: true,
  showErrorToast: true,
  successMessage: '操作成功'
})
```

### 错误处理

- 全局错误边界捕获应用错误
- API 错误统一处理和提示
- 表单验证和错误提示

### 样式规范

- 使用 Semi UI 组件库
- 响应式设计
- 统一的间距和颜色规范

## 浏览器支持

- Chrome >= 88
- Firefox >= 85
- Safari >= 14
- Edge >= 88

## 许可证

MIT License
