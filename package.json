{"name": "shortvideomanager", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "keywords": [], "author": "", "license": "ISC", "description": "Short Video Management Platform", "dependencies": {"@douyinfe/semi-icons": "^2.85.0", "@douyinfe/semi-ui": "^2.85.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "axios": "^1.11.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.0", "typescript": "^5.9.2", "vite": "^7.1.2"}, "devDependencies": {"@types/node": "^24.2.1"}}