import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { Layout } from '@douyinfe/semi-ui'
import MainLayout from './components/Layout/MainLayout'
import UserManagement from './pages/UserManagement'
import ContentManagement from './pages/ContentManagement'
import CommentManagement from './pages/CommentManagement'
import Dashboard from './pages/Dashboard'

const { Content } = Layout

function App() {
  return (
    <MainLayout>
      <Content style={{ padding: '24px', minHeight: 'calc(100vh - 64px)' }}>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/users" element={<UserManagement />} />
          <Route path="/contents" element={<ContentManagement />} />
          <Route path="/comments" element={<CommentManagement />} />
        </Routes>
      </Content>
    </MainLayout>
  )
}

export default App
