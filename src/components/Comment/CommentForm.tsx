import React, { useEffect, useState } from 'react'
import {
  Form,
  Input,
  Button,
  Space,
  Select,
  Typography
} from '@douyinfe/semi-ui'
import { Comment, CreateCommentRequest, UpdateCommentRequest, User, Content } from '../../types'
import { userService, contentService } from '../../services'
import { useApi } from '../../hooks/useApi'

const { TextArea } = Input
const { Text } = Typography

interface CommentFormProps {
  comment?: Comment
  onSubmit: (data: CreateCommentRequest | UpdateCommentRequest) => Promise<void>
  onCancel: () => void
  loading?: boolean
  mode: 'create' | 'edit'
  preselectedContentId?: number
  preselectedParentId?: number
}

const CommentForm: React.FC<CommentFormProps> = ({
  comment,
  onSubmit,
  onCancel,
  loading = false,
  mode,
  preselectedContentId,
  preselectedParentId
}) => {
  const [form] = Form.useForm()
  const [users, setUsers] = useState<User[]>([])
  const [contents, setContents] = useState<Content[]>([])

  const { loading: usersLoading, execute: fetchUsers } = useApi(userService.getAllUsers)
  const { loading: contentsLoading, execute: fetchContents } = useApi(contentService.getAllContents)

  useEffect(() => {
    loadUsers()
    loadContents()
  }, [])

  useEffect(() => {
    if (comment && mode === 'edit') {
      form.setValues({
        contentId: comment.contentId,
        userId: comment.userId,
        parentId: comment.parentId,
        commentText: comment.commentText
      })
    } else {
      form.setValues({
        contentId: preselectedContentId,
        parentId: preselectedParentId || null
      })
    }
  }, [comment, mode, preselectedContentId, preselectedParentId, form])

  const loadUsers = async () => {
    try {
      const result = await fetchUsers()
      setUsers(result || [])
    } catch (error) {
      console.error('Failed to load users:', error)
    }
  }

  const loadContents = async () => {
    try {
      const result = await fetchContents()
      setContents(result || [])
    } catch (error) {
      console.error('Failed to load contents:', error)
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      // Convert parentId to null if it's empty
      const submitData = {
        ...values,
        parentId: values.parentId || null
      }
      await onSubmit(submitData)
      if (mode === 'create') {
        form.reset()
        form.setValues({
          contentId: preselectedContentId,
          parentId: preselectedParentId || null
        })
      }
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  const userOptions = users.map(user => ({
    value: user.id,
    label: user.username
  }))

  const contentOptions = contents.map(content => ({
    value: content.id,
    label: `${content.id} - ${content.description.substring(0, 30)}${content.description.length > 30 ? '...' : ''}`
  }))

  return (
    <Form
      form={form}
      onSubmit={handleSubmit}
      labelPosition="left"
      labelWidth={100}
      style={{ maxWidth: 600 }}
    >
      <Form.Select
        field="contentId"
        label="关联内容"
        placeholder="请选择关联的内容"
        rules={[{ required: true, message: '请选择关联的内容' }]}
        optionList={contentOptions}
        filter
        loading={contentsLoading}
        disabled={contentsLoading || !!preselectedContentId}
      />

      <Form.Select
        field="userId"
        label="评论用户"
        placeholder="请选择评论用户"
        rules={[{ required: true, message: '请选择评论用户' }]}
        optionList={userOptions}
        filter
        loading={usersLoading}
        disabled={usersLoading}
      />

      <Form.Input
        field="parentId"
        label="父评论ID"
        placeholder="留空表示主评论，填写ID表示回复评论"
        type="number"
        disabled={!!preselectedParentId}
      />

      <Form.TextArea
        field="commentText"
        label="评论内容"
        placeholder="请输入评论内容"
        rules={[
          { required: true, message: '请输入评论内容' },
          { max: 1000, message: '评论内容不能超过1000个字符' }
        ]}
        maxCount={1000}
        showClear
        autosize={{ minRows: 3, maxRows: 8 }}
      />

      <Form.Slot label=" ">
        <Space>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
          >
            {mode === 'create' ? '发表评论' : '更新评论'}
          </Button>
          <Button onClick={onCancel}>
            取消
          </Button>
        </Space>
      </Form.Slot>
    </Form>
  )
}

export default CommentForm
