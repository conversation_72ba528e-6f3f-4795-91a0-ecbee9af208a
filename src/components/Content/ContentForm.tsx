import React, { useEffect, useState } from 'react'
import {
  Form,
  Input,
  Button,
  Space,
  Select,
  Typography,
  Spin
} from '@douyinfe/semi-ui'
import { Content, CreateContentRequest, UpdateContentRequest, User } from '../../types'
import { userService } from '../../services'
import { useApi } from '../../hooks/useApi'

const { TextArea } = Input
const { Text } = Typography

interface ContentFormProps {
  content?: Content
  onSubmit: (data: CreateContentRequest | UpdateContentRequest) => Promise<void>
  onCancel: () => void
  loading?: boolean
  mode: 'create' | 'edit'
}

const ContentForm: React.FC<ContentFormProps> = ({
  content,
  onSubmit,
  onCancel,
  loading = false,
  mode
}) => {
  const [form] = Form.useForm()
  const [users, setUsers] = useState<User[]>([])

  const { loading: usersLoading, execute: fetchUsers } = useApi(userService.getAllUsers)

  useEffect(() => {
    loadUsers()
  }, [])

  useEffect(() => {
    if (content && mode === 'edit') {
      form.setValues({
        userId: content.userId,
        description: content.description,
        contentType: content.contentType
      })
    }
  }, [content, mode, form])

  const loadUsers = async () => {
    try {
      const result = await fetchUsers()
      setUsers(result || [])
    } catch (error) {
      console.error('Failed to load users:', error)
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      await onSubmit(values)
      if (mode === 'create') {
        form.reset()
      }
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  const userOptions = users.map(user => ({
    value: user.id,
    label: user.username
  }))

  const contentTypeOptions = [
    { value: 'image', label: '图片' },
    { value: 'video', label: '视频' }
  ]

  return (
    <Form
      form={form}
      onSubmit={handleSubmit}
      labelPosition="left"
      labelWidth={100}
      style={{ maxWidth: 600 }}
    >
      <Form.Select
        field="userId"
        label="用户"
        placeholder="请选择用户"
        rules={[{ required: true, message: '请选择用户' }]}
        optionList={userOptions}
        filter
        loading={usersLoading}
        disabled={usersLoading}
      />

      <Form.Select
        field="contentType"
        label="内容类型"
        placeholder="请选择内容类型"
        rules={[{ required: true, message: '请选择内容类型' }]}
        optionList={contentTypeOptions}
      />

      <Form.TextArea
        field="description"
        label="内容描述"
        placeholder="请输入内容描述"
        rules={[
          { required: true, message: '请输入内容描述' },
          { max: 500, message: '描述长度不能超过500个字符' }
        ]}
        maxCount={500}
        showClear
        autosize={{ minRows: 3, maxRows: 6 }}
      />

      <Form.Slot label=" ">
        <Space>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
          >
            {mode === 'create' ? '创建内容' : '更新内容'}
          </Button>
          <Button onClick={onCancel}>
            取消
          </Button>
        </Space>
      </Form.Slot>
    </Form>
  )
}

export default ContentForm
