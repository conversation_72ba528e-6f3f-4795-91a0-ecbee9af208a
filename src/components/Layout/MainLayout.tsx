import React, { useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  Layout,
  Nav,
  Button,
  Avatar,
  Dropdown,
  Typography,
  Space
} from '@douyinfe/semi-ui'
import {
  IconHome,
  IconUser,
  IconVideoListStroked,
  IconComment,
  IconMenu,
  IconMoon,
  IconSun
} from '@douyinfe/semi-icons'

const { Header, Sider, Content } = Layout
const { Title } = Typography

interface MainLayoutProps {
  children: React.ReactNode
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const [collapsed, setCollapsed] = useState(false)
  const [isDark, setIsDark] = useState(false)

  const navItems = [
    {
      itemKey: '/',
      text: '仪表盘',
      icon: <IconHome />
    },
    {
      itemKey: '/users',
      text: '用户管理',
      icon: <IconUser />
    },
    {
      itemKey: '/contents',
      text: '内容管理',
      icon: <IconVideoListStroked />
    },
    {
      itemKey: '/comments',
      text: '评论管理',
      icon: <IconComment />
    }
  ]

  const handleNavClick = (data: any) => {
    navigate(data.itemKey)
  }

  const toggleTheme = () => {
    setIsDark(!isDark)
    // Here you would typically update the theme context or CSS variables
  }

  const toggleCollapse = () => {
    setCollapsed(!collapsed)
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        style={{
          backgroundColor: 'var(--semi-color-nav-bg)',
          borderRight: '1px solid var(--semi-color-border)'
        }}
        collapsed={collapsed}
        onCollapse={setCollapsed}
      >
        <div style={{ 
          padding: '20px', 
          textAlign: 'center',
          borderBottom: '1px solid var(--semi-color-border)'
        }}>
          {!collapsed ? (
            <Title heading={4} style={{ margin: 0, color: 'var(--semi-color-text-0)' }}>
              短视频管理
            </Title>
          ) : (
            <Avatar size="small" style={{ backgroundColor: 'var(--semi-color-primary)' }}>
              短
            </Avatar>
          )}
        </div>
        
        <Nav
          style={{ maxWidth: 220, height: '100%' }}
          defaultSelectedKeys={[location.pathname]}
          selectedKeys={[location.pathname]}
          items={navItems}
          onSelect={handleNavClick}
          footer={{
            collapseButton: true
          }}
        />
      </Sider>

      <Layout>
        <Header
          style={{
            backgroundColor: 'var(--semi-color-bg-1)',
            borderBottom: '1px solid var(--semi-color-border)',
            padding: '0 24px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
        >
          <Space>
            <Button
              theme="borderless"
              icon={<IconMenu />}
              onClick={toggleCollapse}
              style={{ marginRight: 12 }}
            />
            <Title heading={5} style={{ margin: 0 }}>
              {navItems.find(item => item.itemKey === location.pathname)?.text || '短视频管理平台'}
            </Title>
          </Space>

          <Space>
            <Button
              theme="borderless"
              icon={isDark ? <IconSun /> : <IconMoon />}
              onClick={toggleTheme}
            />
            
            <Dropdown
              trigger="click"
              position="bottomRight"
              render={
                <Dropdown.Menu>
                  <Dropdown.Item>个人设置</Dropdown.Item>
                  <Dropdown.Divider />
                  <Dropdown.Item>退出登录</Dropdown.Item>
                </Dropdown.Menu>
              }
            >
              <Avatar size="small" color="blue" style={{ cursor: 'pointer' }}>
                管理员
              </Avatar>
            </Dropdown>
          </Space>
        </Header>

        <Content
          style={{
            padding: '24px',
            backgroundColor: 'var(--semi-color-bg-0)',
            minHeight: 'calc(100vh - 64px)'
          }}
        >
          {children}
        </Content>
      </Layout>
    </Layout>
  )
}

export default MainLayout
