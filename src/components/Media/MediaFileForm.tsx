import React, { useEffect, useState } from 'react'
import {
  Form,
  Input,
  Button,
  Space,
  Select,
  Typography,
  InputNumber,
  Image,
  Card
} from '@douyinfe/semi-ui'
import { MediaFile, CreateMediaFileRequest, UpdateMediaFileRequest, Content } from '../../types'
import { contentService } from '../../services'
import { useApi } from '../../hooks/useApi'
import { isValidUrl } from '../../utils/format'

const { Text } = Typography

interface MediaFileFormProps {
  mediaFile?: MediaFile
  onSubmit: (data: CreateMediaFileRequest | UpdateMediaFileRequest) => Promise<void>
  onCancel: () => void
  loading?: boolean
  mode: 'create' | 'edit'
  preselectedContentId?: number
}

const MediaFileForm: React.FC<MediaFileFormProps> = ({
  mediaFile,
  onSubmit,
  onCancel,
  loading = false,
  mode,
  preselectedContentId
}) => {
  const [form] = Form.useForm()
  const [contents, setContents] = useState<Content[]>([])

  const { loading: contentsLoading, execute: fetchContents } = useApi(contentService.getAllContents)

  useEffect(() => {
    loadContents()
  }, [])

  useEffect(() => {
    if (mediaFile && mode === 'edit') {
      form.setValues({
        contentId: mediaFile.contentId,
        mediaUrl: mediaFile.mediaUrl,
        mediaOrder: mediaFile.mediaOrder
      })
    } else if (preselectedContentId) {
      form.setValues({
        contentId: preselectedContentId,
        mediaOrder: 1
      })
    }
  }, [mediaFile, mode, preselectedContentId, form])

  const loadContents = async () => {
    try {
      const result = await fetchContents()
      setContents(result || [])
    } catch (error) {
      console.error('Failed to load contents:', error)
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      await onSubmit(values)
      if (mode === 'create') {
        form.reset()
        if (preselectedContentId) {
          form.setValues({
            contentId: preselectedContentId,
            mediaOrder: 1
          })
        }
      }
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  const validateUrl = (value: string) => {
    if (!value) return '请输入媒体文件URL'
    if (!isValidUrl(value)) return '请输入有效的URL地址'
    return ''
  }

  const contentOptions = contents.map(content => ({
    value: content.id,
    label: `${content.id} - ${content.description.substring(0, 30)}${content.description.length > 30 ? '...' : ''}`
  }))

  const mediaUrl = Form.useWatch('mediaUrl', form)

  return (
    <Form
      form={form}
      onSubmit={handleSubmit}
      labelPosition="left"
      labelWidth={100}
      style={{ maxWidth: 600 }}
    >
      <Form.Select
        field="contentId"
        label="关联内容"
        placeholder="请选择关联的内容"
        rules={[{ required: true, message: '请选择关联的内容' }]}
        optionList={contentOptions}
        filter
        loading={contentsLoading}
        disabled={contentsLoading || !!preselectedContentId}
      />

      <Form.Input
        field="mediaUrl"
        label="媒体URL"
        placeholder="请输入媒体文件URL地址"
        rules={[
          { required: true, message: '请输入媒体文件URL' },
          { validator: (rule, value) => validateUrl(value) }
        ]}
        showClear
      />

      <Form.InputNumber
        field="mediaOrder"
        label="显示顺序"
        placeholder="请输入显示顺序"
        min={0}
        max={999}
        style={{ width: '100%' }}
        rules={[{ required: true, message: '请输入显示顺序' }]}
      />

      {mediaUrl && isValidUrl(mediaUrl) && (
        <Form.Slot label="媒体预览">
          <Card style={{ maxWidth: 400 }}>
            {mediaUrl.match(/\.(jpg|jpeg|png|gif|webp)$/i) ? (
              <Image
                src={mediaUrl}
                alt="媒体预览"
                style={{ maxWidth: '100%', maxHeight: '200px' }}
                preview
              />
            ) : mediaUrl.match(/\.(mp4|webm|ogg|mov)$/i) ? (
              <video
                src={mediaUrl}
                controls
                style={{ maxWidth: '100%', maxHeight: '200px' }}
              >
                您的浏览器不支持视频播放
              </video>
            ) : (
              <Text type="secondary">无法预览此媒体类型</Text>
            )}
          </Card>
        </Form.Slot>
      )}

      <Form.Slot label=" ">
        <Space>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
          >
            {mode === 'create' ? '添加媒体文件' : '更新媒体文件'}
          </Button>
          <Button onClick={onCancel}>
            取消
          </Button>
        </Space>
      </Form.Slot>
    </Form>
  )
}

export default MediaFileForm
