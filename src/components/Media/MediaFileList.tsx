import React, { useState, useEffect } from 'react'
import {
  Card,
  Button,
  Space,
  Modal,
  Popconfirm,
  Empty,
  Spin,
  Image,
  Typography,
  Row,
  Col,
  Tag
} from '@douyinfe/semi-ui'
import {
  IconPlus,
  IconEdit,
  IconDelete,
  IconImage,
  IconVideoListStroked
} from '@douyinfe/semi-icons'
import { MediaFile, CreateMediaFileRequest, UpdateMediaFileRequest } from '../../types'
import { mediaService } from '../../services'
import { useApi } from '../../hooks/useApi'
import { formatDate } from '../../utils/format'
import MediaFileForm from './MediaFileForm'

const { Text, Title } = Typography

interface MediaFileListProps {
  contentId: number
  contentDescription: string
}

const MediaFileList: React.FC<MediaFileListProps> = ({ contentId, contentDescription }) => {
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([])
  const [modalVisible, setModalVisible] = useState(false)
  const [editingMediaFile, setEditingMediaFile] = useState<MediaFile | undefined>()
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create')

  // API hooks
  const { loading: mediaFilesLoading, execute: fetchMediaFiles } = useApi(
    mediaService.getMediaFilesByContentId
  )
  const { loading: createLoading, execute: createMediaFile } = useApi(
    mediaService.createMediaFile,
    { showSuccessToast: true, successMessage: '媒体文件添加成功' }
  )
  const { loading: updateLoading, execute: updateMediaFile } = useApi(
    mediaService.updateMediaFile,
    { showSuccessToast: true, successMessage: '媒体文件更新成功' }
  )
  const { loading: deleteLoading, execute: deleteMediaFile } = useApi(
    mediaService.deleteMediaFile,
    { showSuccessToast: true, successMessage: '媒体文件删除成功' }
  )

  useEffect(() => {
    loadMediaFiles()
  }, [contentId])

  const loadMediaFiles = async () => {
    try {
      const result = await fetchMediaFiles(contentId)
      const sortedFiles = (result || []).sort((a, b) => a.mediaOrder - b.mediaOrder)
      setMediaFiles(sortedFiles)
    } catch (error) {
      console.error('Failed to load media files:', error)
    }
  }

  const handleCreateMediaFile = () => {
    setModalMode('create')
    setEditingMediaFile(undefined)
    setModalVisible(true)
  }

  const handleEditMediaFile = (mediaFile: MediaFile) => {
    setModalMode('edit')
    setEditingMediaFile(mediaFile)
    setModalVisible(true)
  }

  const handleDeleteMediaFile = async (mediaFileId: number) => {
    try {
      await deleteMediaFile(mediaFileId)
      await loadMediaFiles()
    } catch (error) {
      console.error('Failed to delete media file:', error)
    }
  }

  const handleFormSubmit = async (data: CreateMediaFileRequest | UpdateMediaFileRequest) => {
    try {
      if (modalMode === 'create') {
        await createMediaFile(data as CreateMediaFileRequest)
      } else if (editingMediaFile) {
        await updateMediaFile(editingMediaFile.id, data as UpdateMediaFileRequest)
      }
      setModalVisible(false)
      await loadMediaFiles()
    } catch (error) {
      console.error('Form submission failed:', error)
    }
  }

  const handleModalCancel = () => {
    setModalVisible(false)
    setEditingMediaFile(undefined)
  }

  const getMediaType = (url: string): 'image' | 'video' | 'unknown' => {
    if (url.match(/\.(jpg|jpeg|png|gif|webp)$/i)) return 'image'
    if (url.match(/\.(mp4|webm|ogg|mov)$/i)) return 'video'
    return 'unknown'
  }

  const renderMediaPreview = (mediaFile: MediaFile) => {
    const mediaType = getMediaType(mediaFile.mediaUrl)
    
    switch (mediaType) {
      case 'image':
        return (
          <Image
            src={mediaFile.mediaUrl}
            alt={`媒体文件 ${mediaFile.id}`}
            style={{ width: '100%', height: '150px', objectFit: 'cover' }}
            preview
          />
        )
      case 'video':
        return (
          <video
            src={mediaFile.mediaUrl}
            style={{ width: '100%', height: '150px', objectFit: 'cover' }}
            controls
          >
            您的浏览器不支持视频播放
          </video>
        )
      default:
        return (
          <div style={{ 
            height: '150px', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            backgroundColor: '#f5f5f5'
          }}>
            <Text type="secondary">无法预览</Text>
          </div>
        )
    }
  }

  return (
    <div>
      <Space vertical size="large" style={{ width: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title heading={4}>媒体文件管理</Title>
            <Text type="secondary">内容: {contentDescription}</Text>
          </div>
          <Button
            type="primary"
            icon={<IconPlus />}
            onClick={handleCreateMediaFile}
          >
            添加媒体文件
          </Button>
        </div>

        {mediaFilesLoading ? (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
          </div>
        ) : mediaFiles.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无媒体文件"
          />
        ) : (
          <Row gutter={[16, 16]}>
            {mediaFiles.map((mediaFile) => (
              <Col span={8} key={mediaFile.id}>
                <Card
                  style={{ height: '100%' }}
                  cover={renderMediaPreview(mediaFile)}
                  actions={[
                    <Button
                      key="edit"
                      type="tertiary"
                      size="small"
                      icon={<IconEdit />}
                      onClick={() => handleEditMediaFile(mediaFile)}
                    >
                      编辑
                    </Button>,
                    <Popconfirm
                      key="delete"
                      title="确认删除"
                      content="删除媒体文件后无法恢复，确定要删除吗？"
                      onConfirm={() => handleDeleteMediaFile(mediaFile.id)}
                    >
                      <Button
                        type="danger"
                        size="small"
                        icon={<IconDelete />}
                        loading={deleteLoading}
                      >
                        删除
                      </Button>
                    </Popconfirm>
                  ]}
                >
                  <Space vertical size="small" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text strong>ID: {mediaFile.id}</Text>
                      <Tag
                        color={getMediaType(mediaFile.mediaUrl) === 'image' ? 'blue' : 'green'}
                        prefixIcon={
                          getMediaType(mediaFile.mediaUrl) === 'image' 
                            ? <IconImage /> 
                            : <IconVideoListStroked />
                        }
                      >
                        {getMediaType(mediaFile.mediaUrl) === 'image' ? '图片' : '视频'}
                      </Tag>
                    </div>
                    <Text type="secondary">顺序: {mediaFile.mediaOrder}</Text>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {formatDate(mediaFile.createdAt)}
                    </Text>
                  </Space>
                </Card>
              </Col>
            ))}
          </Row>
        )}
      </Space>

      <Modal
        title={modalMode === 'create' ? '添加媒体文件' : '编辑媒体文件'}
        visible={modalVisible}
        onCancel={handleModalCancel}
        footer={null}
        width={700}
      >
        <MediaFileForm
          mediaFile={editingMediaFile}
          onSubmit={handleFormSubmit}
          onCancel={handleModalCancel}
          loading={createLoading || updateLoading}
          mode={modalMode}
          preselectedContentId={contentId}
        />
      </Modal>
    </div>
  )
}

export default MediaFileList
