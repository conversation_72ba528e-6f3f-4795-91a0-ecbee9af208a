import React, { useEffect } from 'react'
import {
  Form,
  Input,
  Button,
  Space,
  Avatar,
  Typography
} from '@douyinfe/semi-ui'
import { User, CreateUserRequest, UpdateUserRequest } from '../../types'
import { isValidUrl } from '../../utils/format'

const { Text } = Typography

interface UserFormProps {
  user?: User
  onSubmit: (data: CreateUserRequest | UpdateUserRequest) => Promise<void>
  onCancel: () => void
  loading?: boolean
  mode: 'create' | 'edit'
}

const UserForm: React.FC<UserFormProps> = ({
  user,
  onSubmit,
  onCancel,
  loading = false,
  mode
}) => {
  const [form] = Form.useForm()

  useEffect(() => {
    if (user && mode === 'edit') {
      form.setValues({
        username: user.username,
        avatarUrl: user.avatarUrl
      })
    }
  }, [user, mode, form])

  const handleSubmit = async (values: any) => {
    try {
      await onSubmit(values)
      if (mode === 'create') {
        form.reset()
      }
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  const validateUrl = (value: string) => {
    if (!value) return '请输入头像URL'
    if (!isValidUrl(value)) return '请输入有效的URL地址'
    return ''
  }

  const avatarUrl = Form.useWatch('avatarUrl', form)

  return (
    <Form
      form={form}
      onSubmit={handleSubmit}
      labelPosition="left"
      labelWidth={100}
      style={{ maxWidth: 600 }}
    >
      <Form.Input
        field="username"
        label="用户名"
        placeholder="请输入用户名"
        rules={[
          { required: true, message: '请输入用户名' },
          { min: 1, max: 50, message: '用户名长度应在1-50个字符之间' }
        ]}
        showClear
      />

      <Form.Input
        field="avatarUrl"
        label="头像URL"
        placeholder="请输入头像URL地址"
        rules={[
          { required: true, message: '请输入头像URL' },
          { validator: (rule, value) => validateUrl(value) }
        ]}
        showClear
      />

      {avatarUrl && isValidUrl(avatarUrl) && (
        <Form.Slot label="头像预览">
          <Space>
            <Avatar src={avatarUrl} size="large" />
            <Text type="secondary">头像预览</Text>
          </Space>
        </Form.Slot>
      )}

      <Form.Slot label=" ">
        <Space>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
          >
            {mode === 'create' ? '创建用户' : '更新用户'}
          </Button>
          <Button onClick={onCancel}>
            取消
          </Button>
        </Space>
      </Form.Slot>
    </Form>
  )
}

export default UserForm
