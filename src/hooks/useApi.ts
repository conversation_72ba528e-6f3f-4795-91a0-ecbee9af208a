import { useState, useEffect, useCallback } from 'react'
import { Toast } from '@douyinfe/semi-ui'

interface UseApiState<T> {
  data: T | null
  loading: boolean
  error: string | null
}

interface UseApiOptions {
  immediate?: boolean
  onSuccess?: (data: any) => void
  onError?: (error: any) => void
  showSuccessToast?: boolean
  showErrorToast?: boolean
  successMessage?: string
}

export function useApi<T>(
  apiFunction: (...args: any[]) => Promise<T>,
  options: UseApiOptions = {}
) {
  const {
    immediate = false,
    onSuccess,
    onError,
    showSuccessToast = false,
    showErrorToast = true,
    successMessage = '操作成功'
  } = options

  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null
  })

  const execute = useCallback(async (...args: any[]) => {
    setState(prev => ({ ...prev, loading: true, error: null }))
    
    try {
      const result = await apiFunction(...args)
      setState({ data: result, loading: false, error: null })
      
      if (showSuccessToast) {
        Toast.success(successMessage)
      }
      
      if (onSuccess) {
        onSuccess(result)
      }
      
      return result
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || '操作失败'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
      
      if (showErrorToast) {
        Toast.error(errorMessage)
      }
      
      if (onError) {
        onError(error)
      }
      
      throw error
    }
  }, [apiFunction, onSuccess, onError, showSuccessToast, showErrorToast, successMessage])

  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null })
  }, [])

  useEffect(() => {
    if (immediate) {
      execute()
    }
  }, [immediate, execute])

  return {
    ...state,
    execute,
    reset
  }
}
