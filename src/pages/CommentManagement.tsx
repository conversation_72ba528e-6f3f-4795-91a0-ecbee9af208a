import React, { useState, useEffect } from 'react'
import {
  Typo<PERSON>,
  Table,
  Button,
  Space,
  Input,
  Modal,
  Popconfirm,
  Card,
  Select,
  Tag,
  Empty,
  Spin,
  Tooltip,
  Avatar
} from '@douyinfe/semi-ui'
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconDelete,
  IconRefresh,
  IconComment,
  IconReply
} from '@douyinfe/semi-icons'
import { Comment, CreateCommentRequest, UpdateCommentRequest, User, Content } from '../types'
import { commentService, userService, contentService } from '../services'
import { useApi } from '../hooks/useApi'
import { formatDate, truncateText } from '../utils/format'
import CommentForm from '../components/Comment/CommentForm'

const { Title, Text } = Typography
const { Column } = Table

const CommentManagement: React.FC = () => {
  const [comments, setComments] = useState<Comment[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [contents, setContents] = useState<Content[]>([])
  const [searchText, setSearchText] = useState('')
  const [selectedUserId, setSelectedUserId] = useState<number | undefined>()
  const [selectedContentId, setSelectedContentId] = useState<number | undefined>()
  const [modalVisible, setModalVisible] = useState(false)
  const [editingComment, setEditingComment] = useState<Comment | undefined>()
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create')

  // API hooks
  const { loading: commentsLoading, execute: fetchComments } = useApi(commentService.getAllComments)
  const { loading: usersLoading, execute: fetchUsers } = useApi(userService.getAllUsers)
  const { loading: contentsLoading, execute: fetchContents } = useApi(contentService.getAllContents)
  const { loading: createLoading, execute: createComment } = useApi(
    commentService.createComment,
    { showSuccessToast: true, successMessage: '评论创建成功' }
  )
  const { loading: updateLoading, execute: updateComment } = useApi(
    commentService.updateComment,
    { showSuccessToast: true, successMessage: '评论更新成功' }
  )
  const { loading: deleteLoading, execute: deleteComment } = useApi(
    commentService.deleteComment,
    { showSuccessToast: true, successMessage: '评论删除成功' }
  )

  // Load data on component mount
  useEffect(() => {
    loadComments()
    loadUsers()
    loadContents()
  }, [])

  const loadComments = async () => {
    try {
      const result = await fetchComments()
      setComments(result || [])
    } catch (error) {
      console.error('Failed to load comments:', error)
    }
  }

  const loadUsers = async () => {
    try {
      const result = await fetchUsers()
      setUsers(result || [])
    } catch (error) {
      console.error('Failed to load users:', error)
    }
  }

  const loadContents = async () => {
    try {
      const result = await fetchContents()
      setContents(result || [])
    } catch (error) {
      console.error('Failed to load contents:', error)
    }
  }

  const handleSearch = () => {
    // Filter comments based on search criteria
    let filtered = comments

    if (searchText.trim()) {
      filtered = filtered.filter(comment =>
        comment.commentText.toLowerCase().includes(searchText.toLowerCase())
      )
    }

    if (selectedUserId) {
      filtered = filtered.filter(comment => comment.userId === selectedUserId)
    }

    if (selectedContentId) {
      filtered = filtered.filter(comment => comment.contentId === selectedContentId)
    }

    setComments(filtered)
  }

  const handleRefresh = () => {
    setSearchText('')
    setSelectedUserId(undefined)
    setSelectedContentId(undefined)
    loadComments()
  }

  const handleCreateComment = () => {
    setModalMode('create')
    setEditingComment(undefined)
    setModalVisible(true)
  }

  const handleEditComment = (comment: Comment) => {
    setModalMode('edit')
    setEditingComment(comment)
    setModalVisible(true)
  }

  const handleDeleteComment = async (commentId: number) => {
    try {
      await deleteComment(commentId)
      await loadComments()
    } catch (error) {
      console.error('Failed to delete comment:', error)
    }
  }

  const handleFormSubmit = async (data: CreateCommentRequest | UpdateCommentRequest) => {
    try {
      if (modalMode === 'create') {
        await createComment(data as CreateCommentRequest)
      } else if (editingComment) {
        await updateComment(editingComment.id, data as UpdateCommentRequest)
      }
      setModalVisible(false)
      await loadComments()
    } catch (error) {
      console.error('Form submission failed:', error)
    }
  }

  const handleModalCancel = () => {
    setModalVisible(false)
    setEditingComment(undefined)
  }

  const getUserName = (userId: number) => {
    const user = users.find(u => u.id === userId)
    return user?.username || `用户${userId}`
  }

  const getUserAvatar = (userId: number) => {
    const user = users.find(u => u.id === userId)
    return user?.avatarUrl
  }

  const getContentDescription = (contentId: number) => {
    const content = contents.find(c => c.id === contentId)
    return content?.description || `内容${contentId}`
  }

  const userOptions = users.map(user => ({
    value: user.id,
    label: user.username
  }))

  const contentOptions = contents.map(content => ({
    value: content.id,
    label: `${content.id} - ${content.description.substring(0, 30)}${content.description.length > 30 ? '...' : ''}`
  }))

  return (
    <div>
      <Space vertical size="large" style={{ width: '100%' }}>
        <div>
          <Title heading={3}>评论管理</Title>
          <Text type="secondary">管理系统中的所有评论</Text>
        </div>

        <Card>
          <Space wrap style={{ marginBottom: 16 }}>
            <Input
              prefix={<IconSearch />}
              placeholder="搜索评论内容"
              value={searchText}
              onChange={setSearchText}
              onEnterPress={handleSearch}
              style={{ width: 250 }}
              showClear
            />
            <Select
              placeholder="选择用户"
              value={selectedUserId}
              onChange={setSelectedUserId}
              optionList={userOptions}
              style={{ width: 150 }}
              showClear
            />
            <Select
              placeholder="选择内容"
              value={selectedContentId}
              onChange={setSelectedContentId}
              optionList={contentOptions}
              style={{ width: 200 }}
              showClear
            />
            <Button onClick={handleSearch}>搜索</Button>
            <Button icon={<IconRefresh />} onClick={handleRefresh}>
              刷新
            </Button>
            <Button
              type="primary"
              icon={<IconPlus />}
              onClick={handleCreateComment}
            >
              创建评论
            </Button>
          </Space>

          {commentsLoading ? (
            <div style={{ textAlign: 'center', padding: '50px' }}>
              <Spin size="large" />
            </div>
          ) : comments.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无评论数据"
            />
          ) : (
            <Table
              dataSource={comments}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }}
              loading={commentsLoading}
            >
              <Column
                title="评论ID"
                dataIndex="id"
                key="id"
                width={120}
              />
              <Column
                title="用户"
                dataIndex="userId"
                key="userId"
                render={(userId: number) => (
                  <Space>
                    <Avatar src={getUserAvatar(userId)} size="small" />
                    <Text>{getUserName(userId)}</Text>
                  </Space>
                )}
                width={150}
              />
              <Column
                title="内容"
                dataIndex="contentId"
                key="contentId"
                render={(contentId: number) => (
                  <Tooltip content={getContentDescription(contentId)}>
                    <Text>{truncateText(getContentDescription(contentId), 20)}</Text>
                  </Tooltip>
                )}
                width={150}
              />
              <Column
                title="类型"
                dataIndex="parentId"
                key="parentId"
                render={(parentId: number | null) => (
                  <Tag
                    color={parentId ? 'orange' : 'blue'}
                    prefixIcon={parentId ? <IconReply /> : <IconComment />}
                  >
                    {parentId ? '回复' : '主评论'}
                  </Tag>
                )}
                width={100}
              />
              <Column
                title="父评论"
                dataIndex="parentId"
                key="parentIdDisplay"
                render={(parentId: number | null) => parentId || '-'}
                width={100}
              />
              <Column
                title="评论内容"
                dataIndex="commentText"
                key="commentText"
                render={(text: string) => (
                  <Tooltip content={text}>
                    <Text>{truncateText(text, 50)}</Text>
                  </Tooltip>
                )}
              />
              <Column
                title="创建时间"
                dataIndex="createdAt"
                key="createdAt"
                render={(date: string) => formatDate(date)}
                width={180}
              />
              <Column
                title="操作"
                key="actions"
                render={(_, comment: Comment) => (
                  <Space>
                    <Button
                      type="tertiary"
                      size="small"
                      icon={<IconEdit />}
                      onClick={() => handleEditComment(comment)}
                    >
                      编辑
                    </Button>
                    <Popconfirm
                      title="确认删除"
                      content="删除评论后无法恢复，确定要删除吗？"
                      onConfirm={() => handleDeleteComment(comment.id)}
                    >
                      <Button
                        type="danger"
                        size="small"
                        icon={<IconDelete />}
                        loading={deleteLoading}
                      >
                        删除
                      </Button>
                    </Popconfirm>
                  </Space>
                )}
                width={150}
              />
            </Table>
          )}
        </Card>
      </Space>

      <Modal
        title={modalMode === 'create' ? '创建评论' : '编辑评论'}
        visible={modalVisible}
        onCancel={handleModalCancel}
        footer={null}
        width={700}
      >
        <CommentForm
          comment={editingComment}
          onSubmit={handleFormSubmit}
          onCancel={handleModalCancel}
          loading={createLoading || updateLoading}
          mode={modalMode}
        />
      </Modal>
    </div>
  )
}

export default CommentManagement
