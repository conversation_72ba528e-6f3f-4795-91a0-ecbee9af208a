import React, { useState, useEffect } from 'react'
import {
  Typography,
  Table,
  Button,
  Space,
  Input,
  Modal,
  Popconfirm,
  Card,
  Select,
  Tag,
  Empty,
  Spin,
  Tooltip
} from '@douyinfe/semi-ui'
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconDelete,
  IconRefresh,
  IconImage,
  IconVideoListStroked,
  IconFolder
} from '@douyinfe/semi-icons'
import { Content, CreateContentRequest, UpdateContentRequest, User } from '../types'
import { contentService, userService } from '../services'
import { useApi } from '../hooks/useApi'
import { formatDate, getContentTypeLabel, truncateText } from '../utils/format'
import ContentForm from '../components/Content/ContentForm'
import MediaFileList from '../components/Media/MediaFileList'

const { Title, Text } = Typography
const { Column } = Table

const ContentManagement: React.FC = () => {
  const [contents, setContents] = useState<Content[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [searchText, setSearchText] = useState('')
  const [selectedUserId, setSelectedUserId] = useState<number | undefined>()
  const [selectedContentType, setSelectedContentType] = useState<'image' | 'video' | undefined>()
  const [modalVisible, setModalVisible] = useState(false)
  const [editingContent, setEditingContent] = useState<Content | undefined>()
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create')
  const [mediaModalVisible, setMediaModalVisible] = useState(false)
  const [selectedContent, setSelectedContent] = useState<Content | undefined>()

  // API hooks
  const { loading: contentsLoading, execute: fetchContents } = useApi(contentService.getAllContents)
  const { loading: usersLoading, execute: fetchUsers } = useApi(userService.getAllUsers)
  const { loading: createLoading, execute: createContent } = useApi(
    contentService.createContent,
    { showSuccessToast: true, successMessage: '内容创建成功' }
  )
  const { loading: updateLoading, execute: updateContent } = useApi(
    contentService.updateContent,
    { showSuccessToast: true, successMessage: '内容更新成功' }
  )
  const { loading: deleteLoading, execute: deleteContent } = useApi(
    contentService.deleteContent,
    { showSuccessToast: true, successMessage: '内容删除成功' }
  )

  // Load data on component mount
  useEffect(() => {
    loadContents()
    loadUsers()
  }, [])

  const loadContents = async () => {
    try {
      const result = await fetchContents()
      setContents(result || [])
    } catch (error) {
      console.error('Failed to load contents:', error)
    }
  }

  const loadUsers = async () => {
    try {
      const result = await fetchUsers()
      setUsers(result || [])
    } catch (error) {
      console.error('Failed to load users:', error)
    }
  }

  const handleSearch = () => {
    // This would typically be handled by the API with proper filtering
    // For now, we'll filter on the frontend
    let filtered = contents

    if (searchText.trim()) {
      filtered = filtered.filter(content =>
        content.description.toLowerCase().includes(searchText.toLowerCase())
      )
    }

    if (selectedUserId) {
      filtered = filtered.filter(content => content.userId === selectedUserId)
    }

    if (selectedContentType) {
      filtered = filtered.filter(content => content.contentType === selectedContentType)
    }

    setContents(filtered)
  }

  const handleRefresh = () => {
    setSearchText('')
    setSelectedUserId(undefined)
    setSelectedContentType(undefined)
    loadContents()
  }

  const handleCreateContent = () => {
    setModalMode('create')
    setEditingContent(undefined)
    setModalVisible(true)
  }

  const handleEditContent = (content: Content) => {
    setModalMode('edit')
    setEditingContent(content)
    setModalVisible(true)
  }

  const handleDeleteContent = async (contentId: number) => {
    try {
      await deleteContent(contentId)
      await loadContents()
    } catch (error) {
      console.error('Failed to delete content:', error)
    }
  }

  const handleFormSubmit = async (data: CreateContentRequest | UpdateContentRequest) => {
    try {
      if (modalMode === 'create') {
        await createContent(data as CreateContentRequest)
      } else if (editingContent) {
        await updateContent(editingContent.id, data as UpdateContentRequest)
      }
      setModalVisible(false)
      await loadContents()
    } catch (error) {
      console.error('Form submission failed:', error)
    }
  }

  const handleModalCancel = () => {
    setModalVisible(false)
    setEditingContent(undefined)
  }

  const handleManageMedia = (content: Content) => {
    setSelectedContent(content)
    setMediaModalVisible(true)
  }

  const handleMediaModalCancel = () => {
    setMediaModalVisible(false)
    setSelectedContent(undefined)
  }

  const getUserName = (userId: number) => {
    const user = users.find(u => u.id === userId)
    return user?.username || `用户${userId}`
  }

  const userOptions = users.map(user => ({
    value: user.id,
    label: user.username
  }))

  const contentTypeOptions = [
    { value: 'image', label: '图片' },
    { value: 'video', label: '视频' }
  ]

  return (
    <div>
      <Space vertical size="large" style={{ width: '100%' }}>
        <div>
          <Title heading={3}>内容管理</Title>
          <Text type="secondary">管理系统中的所有内容</Text>
        </div>

        <Card>
          <Space wrap style={{ marginBottom: 16 }}>
            <Input
              prefix={<IconSearch />}
              placeholder="搜索内容描述"
              value={searchText}
              onChange={setSearchText}
              onEnterPress={handleSearch}
              style={{ width: 250 }}
              showClear
            />
            <Select
              placeholder="选择用户"
              value={selectedUserId}
              onChange={setSelectedUserId}
              optionList={userOptions}
              style={{ width: 150 }}
              showClear
            />
            <Select
              placeholder="内容类型"
              value={selectedContentType}
              onChange={setSelectedContentType}
              optionList={contentTypeOptions}
              style={{ width: 120 }}
              showClear
            />
            <Button onClick={handleSearch}>搜索</Button>
            <Button icon={<IconRefresh />} onClick={handleRefresh}>
              刷新
            </Button>
            <Button
              type="primary"
              icon={<IconPlus />}
              onClick={handleCreateContent}
            >
              创建内容
            </Button>
          </Space>

          {contentsLoading ? (
            <div style={{ textAlign: 'center', padding: '50px' }}>
              <Spin size="large" />
            </div>
          ) : contents.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无内容数据"
            />
          ) : (
            <Table
              dataSource={contents}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }}
              loading={contentsLoading}
            >
              <Column
                title="内容ID"
                dataIndex="id"
                key="id"
                width={120}
              />
              <Column
                title="用户"
                dataIndex="userId"
                key="userId"
                render={(userId: number) => getUserName(userId)}
                width={120}
              />
              <Column
                title="类型"
                dataIndex="contentType"
                key="contentType"
                render={(type: 'image' | 'video') => (
                  <Tag
                    color={type === 'image' ? 'blue' : 'green'}
                    prefixIcon={type === 'image' ? <IconImage /> : <IconVideoListStroked />}
                  >
                    {getContentTypeLabel(type)}
                  </Tag>
                )}
                width={100}
              />
              <Column
                title="描述"
                dataIndex="description"
                key="description"
                render={(description: string) => (
                  <Tooltip content={description}>
                    <Text>{truncateText(description, 50)}</Text>
                  </Tooltip>
                )}
              />
              <Column
                title="创建时间"
                dataIndex="createdAt"
                key="createdAt"
                render={(date: string) => formatDate(date)}
                width={180}
              />
              <Column
                title="操作"
                key="actions"
                render={(_, content: Content) => (
                  <Space>
                    <Button
                      type="tertiary"
                      size="small"
                      icon={<IconFolder />}
                      onClick={() => handleManageMedia(content)}
                    >
                      媒体
                    </Button>
                    <Button
                      type="tertiary"
                      size="small"
                      icon={<IconEdit />}
                      onClick={() => handleEditContent(content)}
                    >
                      编辑
                    </Button>
                    <Popconfirm
                      title="确认删除"
                      content="删除内容后无法恢复，确定要删除吗？"
                      onConfirm={() => handleDeleteContent(content.id)}
                    >
                      <Button
                        type="danger"
                        size="small"
                        icon={<IconDelete />}
                        loading={deleteLoading}
                      >
                        删除
                      </Button>
                    </Popconfirm>
                  </Space>
                )}
                width={200}
              />
            </Table>
          )}
        </Card>
      </Space>

      <Modal
        title={modalMode === 'create' ? '创建内容' : '编辑内容'}
        visible={modalVisible}
        onCancel={handleModalCancel}
        footer={null}
        width={600}
      >
        <ContentForm
          content={editingContent}
          onSubmit={handleFormSubmit}
          onCancel={handleModalCancel}
          loading={createLoading || updateLoading}
          mode={modalMode}
        />
      </Modal>

      <Modal
        title="媒体文件管理"
        visible={mediaModalVisible}
        onCancel={handleMediaModalCancel}
        footer={null}
        width={1000}
        style={{ top: 20 }}
      >
        {selectedContent && (
          <MediaFileList
            contentId={selectedContent.id}
            contentDescription={selectedContent.description}
          />
        )}
      </Modal>
    </div>
  )
}

export default ContentManagement
