import React, { useEffect, useState } from 'react'
import {
  Card,
  Row,
  Col,
  Typography,
  Space,
  Spin,
  Empty
} from '@douyinfe/semi-ui'
import {
  IconUser,
  IconVideoListStroked,
  IconComment,
  IconImage
} from '@douyinfe/semi-icons'
import { userService, contentService, commentService } from '../services'
import { useApi } from '../hooks/useApi'

const { Title, Text } = Typography

interface DashboardStats {
  totalUsers: number
  totalContents: number
  totalComments: number
  imageContents: number
  videoContents: number
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalContents: 0,
    totalComments: 0,
    imageContents: 0,
    videoContents: 0
  })

  const { loading: usersLoading, execute: fetchUsers } = useApi(userService.getAllUsers)
  const { loading: contentsLoading, execute: fetchContents } = useApi(contentService.getAllContents)
  const { loading: commentsLoading, execute: fetchComments } = useApi(commentService.getAllComments)

  const loading = usersLoading || contentsLoading || commentsLoading

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        const [users, contents, comments] = await Promise.all([
          fetchUsers(),
          fetchContents(),
          fetchComments()
        ])

        const imageContents = contents?.filter(c => c.contentType === 'image').length || 0
        const videoContents = contents?.filter(c => c.contentType === 'video').length || 0

        setStats({
          totalUsers: users?.length || 0,
          totalContents: contents?.length || 0,
          totalComments: comments?.length || 0,
          imageContents,
          videoContents
        })
      } catch (error) {
        console.error('Failed to load dashboard data:', error)
      }
    }

    loadDashboardData()
  }, [])

  const statCards = [
    {
      title: '总用户数',
      value: stats.totalUsers,
      icon: <IconUser size="large" />,
      color: '#1890ff'
    },
    {
      title: '总内容数',
      value: stats.totalContents,
      icon: <IconVideoListStroked size="large" />,
      color: '#52c41a'
    },
    {
      title: '总评论数',
      value: stats.totalComments,
      icon: <IconComment size="large" />,
      color: '#faad14'
    },
    {
      title: '图片内容',
      value: stats.imageContents,
      icon: <IconImage size="large" />,
      color: '#722ed1'
    }
  ]

  return (
    <div>
      <Space vertical size="large" style={{ width: '100%' }}>
        <div>
          <Title heading={3}>仪表盘</Title>
          <Text type="secondary">欢迎使用短视频管理平台</Text>
        </div>

        {loading ? (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
          </div>
        ) : (
          <Row gutter={[16, 16]}>
            {statCards.map((card, index) => (
              <Col span={6} key={index}>
                <Card
                  style={{ textAlign: 'center' }}
                  bodyStyle={{ padding: '24px' }}
                >
                  <Space vertical size="medium">
                    <div style={{ color: card.color }}>
                      {card.icon}
                    </div>
                  </Space>
                </Card>
              </Col>
            ))}
          </Row>
        )}

        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Card title="内容类型分布" style={{ height: '300px' }}>
              <div style={{ textAlign: 'center', paddingTop: '50px' }}>
              </div>
            </Card>
          </Col>
          <Col span={12}>
            <Card title="最近活动" style={{ height: '300px' }}>
              <div style={{ textAlign: 'center', paddingTop: '80px' }}>
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="暂无最近活动"
                />
              </div>
            </Card>
          </Col>
        </Row>
      </Space>
    </div>
  )
}

export default Dashboard
