import React, { useState, useEffect } from 'react'
import {
  <PERSON>po<PERSON>,
  Table,
  Button,
  Space,
  Input,
  Modal,
  Avatar,
  Popconfirm,
  Card,
  Row,
  Col,
  Empty,
  Spin
} from '@douyinfe/semi-ui'
import {
  IconSearch,
  IconPlus,
  IconEdit,
  IconDelete,
  IconRefresh
} from '@douyinfe/semi-icons'
import { User, CreateUserRequest, UpdateUserRequest, UserQueryParams } from '../types'
import { userService } from '../services'
import { useApi } from '../hooks/useApi'
import { formatDate } from '../utils/format'
import UserForm from '../components/User/UserForm'

const { Title, Text } = Typography
const { Column } = Table

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([])
  const [searchText, setSearchText] = useState('')
  const [modalVisible, setModalVisible] = useState(false)
  const [editingUser, setEditingUser] = useState<User | undefined>()
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create')

  // API hooks
  const { loading: usersLoading, execute: fetchUsers } = useApi(userService.getAllUsers)
  const { loading: createLoading, execute: createUser } = useApi(
    userService.createUser,
    { showSuccessToast: true, successMessage: '用户创建成功' }
  )
  const { loading: updateLoading, execute: updateUser } = useApi(
    userService.updateUser,
    { showSuccessToast: true, successMessage: '用户更新成功' }
  )
  const { loading: deleteLoading, execute: deleteUser } = useApi(
    userService.deleteUser,
    { showSuccessToast: true, successMessage: '用户删除成功' }
  )

  // Load users on component mount
  useEffect(() => {
    loadUsers()
  }, [])

  const loadUsers = async () => {
    try {
      const result = await fetchUsers()
      setUsers(result || [])
    } catch (error) {
      console.error('Failed to load users:', error)
    }
  }

  const handleSearch = () => {
    if (!searchText.trim()) {
      loadUsers()
      return
    }

    const filteredUsers = users.filter(user =>
      user.username.toLowerCase().includes(searchText.toLowerCase())
    )
    setUsers(filteredUsers)
  }

  const handleRefresh = () => {
    setSearchText('')
    loadUsers()
  }

  const handleCreateUser = () => {
    setModalMode('create')
    setEditingUser(undefined)
    setModalVisible(true)
  }

  const handleEditUser = (user: User) => {
    setModalMode('edit')
    setEditingUser(user)
    setModalVisible(true)
  }

  const handleDeleteUser = async (userId: number) => {
    try {
      await deleteUser(userId)
      await loadUsers()
    } catch (error) {
      console.error('Failed to delete user:', error)
    }
  }

  const handleFormSubmit = async (data: CreateUserRequest | UpdateUserRequest) => {
    try {
      if (modalMode === 'create') {
        await createUser(data as CreateUserRequest)
      } else if (editingUser) {
        await updateUser(editingUser.id, data as UpdateUserRequest)
      }
      setModalVisible(false)
      await loadUsers()
    } catch (error) {
      console.error('Form submission failed:', error)
    }
  }

  const handleModalCancel = () => {
    setModalVisible(false)
    setEditingUser(undefined)
  }

  const filteredUsers = searchText
    ? users.filter(user =>
        user.username.toLowerCase().includes(searchText.toLowerCase())
      )
    : users

  return (
    <div>
      <Space vertical size="large" style={{ width: '100%' }}>
        <div>
          <Title heading={3}>用户管理</Title>
          <Text type="secondary">管理系统中的所有用户</Text>
        </div>

        <Card>
          <Space style={{ marginBottom: 16 }}>
            <Input
              prefix={<IconSearch />}
              placeholder="搜索用户名"
              value={searchText}
              onChange={setSearchText}
              onEnterPress={handleSearch}
              style={{ width: 300 }}
              showClear
            />
            <Button onClick={handleSearch}>搜索</Button>
            <Button icon={<IconRefresh />} onClick={handleRefresh}>
              刷新
            </Button>
            <Button
              type="primary"
              icon={<IconPlus />}
              onClick={handleCreateUser}
            >
              创建用户
            </Button>
          </Space>

          {usersLoading ? (
            <div style={{ textAlign: 'center', padding: '50px' }}>
              <Spin size="large" />
            </div>
          ) : filteredUsers.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无用户数据"
            />
          ) : (
            <Table
              dataSource={filteredUsers}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }}
              loading={usersLoading}
            >
              <Column
                title="头像"
                dataIndex="avatarUrl"
                key="avatar"
                render={(avatarUrl: string) => (
                  <Avatar src={avatarUrl} size="small" />
                )}
                width={80}
              />
              <Column
                title="用户ID"
                dataIndex="id"
                key="id"
                width={150}
              />
              <Column
                title="用户名"
                dataIndex="username"
                key="username"
              />
              <Column
                title="创建时间"
                dataIndex="createdAt"
                key="createdAt"
                render={(date: string) => formatDate(date)}
                width={180}
              />
              <Column
                title="更新时间"
                dataIndex="updatedAt"
                key="updatedAt"
                render={(date: string) => formatDate(date)}
                width={180}
              />
              <Column
                title="操作"
                key="actions"
                render={(_, user: User) => (
                  <Space>
                    <Button
                      type="tertiary"
                      size="small"
                      icon={<IconEdit />}
                      onClick={() => handleEditUser(user)}
                    >
                      编辑
                    </Button>
                    <Popconfirm
                      title="确认删除"
                      content="删除用户后无法恢复，确定要删除吗？"
                      onConfirm={() => handleDeleteUser(user.id)}
                    >
                      <Button
                        type="danger"
                        size="small"
                        icon={<IconDelete />}
                        loading={deleteLoading}
                      >
                        删除
                      </Button>
                    </Popconfirm>
                  </Space>
                )}
                width={150}
              />
            </Table>
          )}
        </Card>
      </Space>

      <Modal
        title={modalMode === 'create' ? '创建用户' : '编辑用户'}
        visible={modalVisible}
        onCancel={handleModalCancel}
        footer={null}
        width={600}
      >
        <UserForm
          user={editingUser}
          onSubmit={handleFormSubmit}
          onCancel={handleModalCancel}
          loading={createLoading || updateLoading}
          mode={modalMode}
        />
      </Modal>
    </div>
  )
}

export default UserManagement
