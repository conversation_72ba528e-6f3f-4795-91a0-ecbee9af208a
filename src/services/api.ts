import axios, { AxiosResponse } from 'axios'
import type {
  ApiResponse,
  PaginatedResponse,
  User,
  CreateUserRequest,
  UpdateUserRequest,
  UserQueryParams,
  Content,
  CreateContentRequest,
  UpdateContentRequest,
  ContentQueryParams,
  MediaFile,
  CreateMediaFileRequest,
  UpdateMediaFileRequest,
  MediaFileQueryParams,
  Comment,
  CreateCommentRequest,
  UpdateCommentRequest,
  CommentQueryParams
} from '../types'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:8080',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: 10000
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    console.log(`API Response: ${response.status} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// Generic API call function
const apiCall = async <T>(
  method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  url: string,
  data?: any,
  params?: any
): Promise<T> => {
  try {
    const response = await api.request<ApiResponse<T>>({
      method,
      url,
      data,
      params
    })
    
    if (response.data.code !== 200) {
      throw new Error(response.data.message || 'API request failed')
    }
    
    return response.data.data
  } catch (error) {
    console.error(`API ${method} ${url} failed:`, error)
    throw error
  }
}

export { api, apiCall }
