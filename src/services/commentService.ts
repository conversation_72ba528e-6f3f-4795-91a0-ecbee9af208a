import { apiCall } from './api'
import type {
  Comment,
  CreateCommentRequest,
  UpdateCommentRequest,
  CommentQueryParams,
  PaginatedResponse
} from '../types'

export const commentService = {
  // Create comment
  createComment: async (commentData: CreateCommentRequest): Promise<Comment> => {
    return apiCall<Comment>('POST', '/api/comments', commentData)
  },

  // Get comment by ID
  getCommentById: async (id: number): Promise<Comment> => {
    return apiCall<Comment>('GET', `/api/comments/${id}`)
  },

  // Get paginated comments
  getComments: async (params?: CommentQueryParams): Promise<PaginatedResponse<Comment>> => {
    return apiCall<PaginatedResponse<Comment>>('GET', '/api/comments', undefined, params)
  },

  // Get comments by content ID
  getCommentsByContentId: async (contentId: number): Promise<Comment[]> => {
    return apiCall<Comment[]>('GET', `/api/comments/content/${contentId}`)
  },

  // Get comments by user ID
  getCommentsByUserId: async (userId: number): Promise<Comment[]> => {
    return apiCall<Comment[]>('GET', `/api/comments/user/${userId}`)
  },

  // Get comments by parent ID (replies)
  getCommentsByParentId: async (parentId: number): Promise<Comment[]> => {
    return apiCall<Comment[]>('GET', `/api/comments/parent/${parentId}`)
  },

  // Get all comments
  getAllComments: async (): Promise<Comment[]> => {
    return apiCall<Comment[]>('GET', '/api/comments/all')
  },

  // Update comment
  updateComment: async (id: number, commentData: UpdateCommentRequest): Promise<Comment> => {
    return apiCall<Comment>('PUT', `/api/comments/${id}`, commentData)
  },

  // Delete comment
  deleteComment: async (id: number): Promise<void> => {
    return apiCall<void>('DELETE', `/api/comments/${id}`)
  },

  // Delete all comments by content ID
  deleteCommentsByContentId: async (contentId: number): Promise<void> => {
    return apiCall<void>('DELETE', `/api/comments/content/${contentId}`)
  }
}
