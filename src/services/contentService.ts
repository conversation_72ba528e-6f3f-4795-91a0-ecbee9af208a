import { apiCall } from './api'
import type {
  Content,
  CreateContentRequest,
  UpdateContentRequest,
  ContentQueryParams,
  PaginatedResponse
} from '../types'

export const contentService = {
  // Create content
  createContent: async (contentData: CreateContentRequest): Promise<Content> => {
    return apiCall<Content>('POST', '/api/contents', contentData)
  },

  // Get content by ID
  getContentById: async (id: number): Promise<Content> => {
    return apiCall<Content>('GET', `/api/contents/${id}`)
  },

  // Get paginated contents
  getContents: async (params?: ContentQueryParams): Promise<PaginatedResponse<Content>> => {
    return apiCall<PaginatedResponse<Content>>('GET', '/api/contents', undefined, params)
  },

  // Get contents by user ID
  getContentsByUserId: async (userId: number): Promise<Content[]> => {
    return apiCall<Content[]>('GET', `/api/contents/user/${userId}`)
  },

  // Get contents by type
  getContentsByType: async (contentType: 'image' | 'video'): Promise<Content[]> => {
    return apiCall<Content[]>('GET', `/api/contents/type/${contentType}`)
  },

  // Get all contents
  getAllContents: async (): Promise<Content[]> => {
    return apiCall<Content[]>('GET', '/api/contents/all')
  },

  // Update content
  updateContent: async (id: number, contentData: UpdateContentRequest): Promise<Content> => {
    return apiCall<Content>('PUT', `/api/contents/${id}`, contentData)
  },

  // Delete content
  deleteContent: async (id: number): Promise<void> => {
    return apiCall<void>('DELETE', `/api/contents/${id}`)
  }
}
