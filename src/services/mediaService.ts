import { apiCall } from './api'
import type {
  MediaFile,
  CreateMediaFileRequest,
  UpdateMediaFileRequest,
  MediaFileQueryParams,
  PaginatedResponse
} from '../types'

export const mediaService = {
  // Create media file
  createMediaFile: async (mediaData: CreateMediaFileRequest): Promise<MediaFile> => {
    return apiCall<MediaFile>('POST', '/api/content-media', mediaData)
  },

  // Get media file by ID
  getMediaFileById: async (id: number): Promise<MediaFile> => {
    return apiCall<MediaFile>('GET', `/api/content-media/${id}`)
  },

  // Get paginated media files
  getMediaFiles: async (params?: MediaFileQueryParams): Promise<PaginatedResponse<MediaFile>> => {
    return apiCall<PaginatedResponse<MediaFile>>('GET', '/api/content-media', undefined, params)
  },

  // Get media files by content ID
  getMediaFilesByContentId: async (contentId: number): Promise<MediaFile[]> => {
    return apiCall<MediaFile[]>('GET', `/api/content-media/content/${contentId}`)
  },

  // Get all media files
  getAllMediaFiles: async (): Promise<MediaFile[]> => {
    return apiCall<MediaFile[]>('GET', '/api/content-media/all')
  },

  // Update media file
  updateMediaFile: async (id: number, mediaData: UpdateMediaFileRequest): Promise<MediaFile> => {
    return apiCall<MediaFile>('PUT', `/api/content-media/${id}`, mediaData)
  },

  // Delete media file
  deleteMediaFile: async (id: number): Promise<void> => {
    return apiCall<void>('DELETE', `/api/content-media/${id}`)
  },

  // Delete all media files by content ID
  deleteMediaFilesByContentId: async (contentId: number): Promise<void> => {
    return apiCall<void>('DELETE', `/api/content-media/content/${contentId}`)
  }
}
