import { apiCall } from './api'
import type {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  UserQueryParams,
  PaginatedResponse
} from '../types'

export const userService = {
  // Create user
  createUser: async (userData: CreateUserRequest): Promise<User> => {
    return apiCall<User>('POST', '/api/users', userData)
  },

  // Get user by ID
  getUserById: async (id: number): Promise<User> => {
    return apiCall<User>('GET', `/api/users/${id}`)
  },

  // Get user by username
  getUserByUsername: async (username: string): Promise<User> => {
    return apiCall<User>('GET', `/api/users/username/${username}`)
  },

  // Get paginated users
  getUsers: async (params?: UserQueryParams): Promise<PaginatedResponse<User>> => {
    return apiCall<PaginatedResponse<User>>('GET', '/api/users', undefined, params)
  },

  // Get all users
  getAllUsers: async (): Promise<User[]> => {
    return apiCall<User[]>('GET', '/api/users/all')
  },

  // Update user
  updateUser: async (id: number, userData: UpdateUserRequest): Promise<User> => {
    return apiCall<User>('PUT', `/api/users/${id}`, userData)
  },

  // Delete user
  deleteUser: async (id: number): Promise<void> => {
    return apiCall<void>('DELETE', `/api/users/${id}`)
  }
}
