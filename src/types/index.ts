// API Response Types
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

export interface PaginatedResponse<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

// User Types
export interface User {
  id: number;
  username: string;
  avatarUrl: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateUserRequest {
  username: string;
  avatarUrl: string;
}

export interface UpdateUserRequest {
  username: string;
  avatarUrl: string;
}

// Content Types
export interface Content {
  id: number;
  userId: number;
  description: string;
  contentType: 'image' | 'video';
  createdAt: string;
  updatedAt: string;
}

export interface CreateContentRequest {
  userId: number;
  description: string;
  contentType: 'image' | 'video';
}

export interface UpdateContentRequest {
  userId: number;
  description: string;
  contentType: 'image' | 'video';
}

// Media File Types
export interface MediaFile {
  id: number;
  contentId: number;
  mediaUrl: string;
  mediaOrder: number;
  createdAt: string;
}

export interface CreateMediaFileRequest {
  contentId: number;
  mediaUrl: string;
  mediaOrder?: number;
}

export interface UpdateMediaFileRequest {
  contentId: number;
  mediaUrl: string;
  mediaOrder: number;
}

// Comment Types
export interface Comment {
  id: number;
  contentId: number;
  userId: number;
  parentId: number | null;
  commentText: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCommentRequest {
  contentId: number;
  userId: number;
  parentId?: number | null;
  commentText: string;
}

export interface UpdateCommentRequest {
  contentId: number;
  userId: number;
  parentId?: number | null;
  commentText: string;
}

// Query Parameters
export interface PaginationParams {
  current?: number;
  size?: number;
}

export interface UserQueryParams extends PaginationParams {
  username?: string;
}

export interface ContentQueryParams extends PaginationParams {
  userId?: number;
  contentType?: 'image' | 'video';
}

export interface MediaFileQueryParams extends PaginationParams {
  contentId?: number;
}

export interface CommentQueryParams extends PaginationParams {
  contentId?: number;
  userId?: number;
  parentId?: number;
}
